# 传奇游戏Boss管理工具 (Mac版)

一个用于自动管理传奇小游戏中多地图Boss的辅助工具，专为Mac系统优化。

## 快速开始

### 1. 安装依赖
```bash
cd fuzhu
pip3 install -r requirements.txt
```

### 2. 获取坐标
```bash
python3 coordinate_helper.py
```
使用坐标获取工具来确定地图按钮和Boss的准确位置。

### 3. 配置设置
编辑 `legend_config.json` 文件，将获取的坐标填入配置。

### 4. 启动工具
```bash
# 方式1: 直接运行
python3 legend_gui.py

# 方式2: 使用脚本
chmod +x run.sh
./run.sh
```

## Mac特殊设置

### 辅助功能权限
1. 打开 `系统偏好设置` > `安全性与隐私` > `隐私`
2. 选择 `辅助功能`
3. 添加 `终端` 或你使用的Python IDE
4. 确保权限已启用

### 屏幕录制权限
1. 在 `隐私` 设置中选择 `屏幕录制`
2. 添加相应的应用程序
3. 重启应用程序使权限生效

## 文件说明

- `legend_game_manager.py` - 核心管理逻辑
- `legend_gui.py` - GUI界面
- `coordinate_helper.py` - 坐标获取工具
- `legend_config.json` - 配置文件
- `requirements.txt` - 依赖包
- `run.sh` - 启动脚本

## 使用技巧

1. **坐标获取**: 使用 `coordinate_helper.py` 精确获取坐标
2. **权限设置**: 确保已授予必要的系统权限
3. **游戏窗口**: 保持游戏窗口在前台
4. **测试模式**: 先在测试环境验证坐标准确性

## 故障排除

### 权限问题
```bash
# 如果遇到权限错误，尝试：
sudo python3 legend_gui.py
```

### 依赖安装问题
```bash
# 使用brew安装opencv
brew install opencv

# 或使用conda
conda install opencv pyautogui
```

### 坐标不准确
- 检查屏幕分辨率设置
- 确认游戏窗口位置
- 重新获取坐标

## 注意事项

- 仅在macOS 10.14+测试
- 需要授予辅助功能权限
- 建议在虚拟环境中运行
- 遵守游戏使用条款