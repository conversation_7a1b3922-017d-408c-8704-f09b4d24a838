# 楚汉传奇自动刷Boss工具

专为微信小程序"楚汉传奇"设计的自动化刷Boss工具，支持Mac系统。

## 功能特点

- 🎯 **自动定位Boss**: 精确点击Boss位置进行攻击
- 🔄 **智能循环**: 自动检测Boss重生时间，循环刷新
- 📊 **优先级管理**: 支持Boss优先级设置，优先攻击高价值目标
- 🖥️ **图形界面**: 友好的GUI界面，操作简单
- 📍 **坐标工具**: 内置坐标获取工具，精确定位Boss位置
- ⏰ **实时状态**: 显示Boss状态和重生倒计时

## 快速开始

### 1. 安装依赖
```bash
cd fuzhu
pip3 install opencv-python pyautogui Pillow numpy
```

### 2. 启动工具
```bash
# 方式1: 使用启动脚本
./start_chuhanlegend.sh

# 方式2: 直接运行
python3 chuhanlegend_gui.py
```

### 3. 配置Boss位置
1. 打开微信小程序"楚汉传奇"
2. 进入要刷Boss的地图
3. 在工具中点击"坐标工具"按钮
4. 使用坐标获取工具标记Boss位置
5. 保存配置

### 4. 开始自动刷Boss
1. 在主界面选择地图
2. 点击"开始自动刷Boss"
3. 工具将自动循环攻击Boss

## 文件说明

- `chuhanlegend_bot.py` - 核心自动化逻辑
- `chuhanlegend_gui.py` - GUI主界面
- `coordinate_picker.py` - 坐标获取工具
- `chuhanlegend_config.json` - 配置文件（自动生成）
- `start_chuhanlegend.sh` - 启动脚本

## 使用步骤详解

### 第一步：获取Boss坐标

1. **启动坐标工具**
   - 运行 `python3 coordinate_picker.py`
   - 或在主界面点击"坐标工具"

2. **标记Boss位置**
   - 点击"获取坐标"按钮
   - 3秒倒计时后点击游戏中的Boss位置
   - 填写Boss信息（名称、重生时间、优先级）
   - 点击"添加Boss"

3. **保存配置**
   - 输入地图名称
   - 点击"保存配置"

### 第二步：配置系统权限

**重要：Mac系统需要授予以下权限**

1. **辅助功能权限**
   - 打开 `系统偏好设置` > `安全性与隐私` > `隐私`
   - 选择 `辅助功能`
   - 添加 `终端` 或你使用的Python IDE
   - 确保权限已启用

2. **屏幕录制权限**
   - 在 `隐私` 设置中选择 `屏幕录制`
   - 添加相应的应用程序
   - 重启应用程序使权限生效

### 第三步：开始自动刷Boss

1. **启动主程序**
   ```bash
   python3 chuhanlegend_gui.py
   ```

2. **选择地图**
   - 在下拉菜单中选择要刷的地图

3. **检查Boss状态**
   - 查看右侧Boss列表
   - 确认Boss位置和状态正确

4. **开始自动化**
   - 点击"开始自动刷Boss"
   - 工具将自动循环攻击可用的Boss

## 配置文件格式

```json
{
  "maps": {
    "萃光高地": {
      "center_position": [640, 400],
      "bosses": [
        {
          "name": "圣堕落之灵",
          "position": [300, 200],
          "respawn_time": 30,
          "priority": 5
        }
      ]
    }
  },
  "settings": {
    "click_delay": 0.5,
    "move_delay": 1.0,
    "attack_duration": 5,
    "scan_interval": 2
  }
}
```

## 参数说明

- `position`: Boss在屏幕上的坐标位置
- `respawn_time`: Boss重生时间（秒）
- `priority`: 优先级（1-5，5最高）
- `click_delay`: 点击间隔时间
- `move_delay`: 移动等待时间
- `attack_duration`: 攻击持续时间
- `scan_interval`: 扫描间隔时间

## 使用技巧

1. **精确定位**
   - 使用坐标工具精确标记Boss位置
   - 建议在Boss静止时获取坐标

2. **优先级设置**
   - 高价值Boss设置高优先级
   - 工具会优先攻击高优先级Boss

3. **重生时间**
   - 根据实际游戏情况设置重生时间
   - 避免无效等待

4. **多地图管理**
   - 可以配置多个地图
   - 切换地图时重新选择即可

## 故障排除

### 权限问题
```bash
# 如果遇到权限错误，尝试：
sudo python3 chuhanlegend_gui.py
```

### 坐标不准确
- 检查屏幕分辨率设置
- 确认游戏窗口位置没有变化
- 重新获取坐标

### 程序无响应
- 检查是否授予了必要的系统权限
- 确认微信小程序在前台运行
- 重启程序

## 注意事项

- ⚠️ 仅在macOS 10.14+测试
- ⚠️ 需要授予辅助功能和屏幕录制权限
- ⚠️ 确保游戏窗口在前台
- ⚠️ 遵守游戏使用条款
- ⚠️ 建议在虚拟环境中运行

## 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 依赖包是否正确安装
3. 系统权限是否已授予
4. 配置文件格式是否正确
