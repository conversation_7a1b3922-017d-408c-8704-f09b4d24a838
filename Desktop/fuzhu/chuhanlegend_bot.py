#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
楚汉传奇自动刷Boss脚本 (Mac版)
适用于微信小程序中的楚汉传奇游戏
"""

import cv2
import numpy as np
import pyautogui
import time
import json
import os
from datetime import datetime
from dataclasses import dataclass
from typing import List, Tuple, Optional

# 禁用pyautogui的安全机制
pyautogui.FAILSAFE = False
pyautogui.PAUSE = 0.1

@dataclass
class Boss:
    """Boss信息"""
    name: str
    position: Tuple[int, int]  # 屏幕坐标
    respawn_time: int  # 重生时间(秒)
    last_killed: Optional[datetime] = None
    priority: int = 1  # 优先级 1-5

@dataclass
class GameMap:
    """地图信息"""
    name: str
    bosses: List[Boss]
    center_position: Tuple[int, int]  # 地图中心位置

class ChuhanLegendBot:
    def __init__(self):
        self.running = False
        self.current_map = None
        self.maps = {}
        self.config_file = "chuhanlegend_config.json"
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.parse_config(config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self.create_default_config()
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        config = {
            "maps": {
                "萃光高地": {
                    "center_position": [640, 400],
                    "bosses": [
                        {"name": "圣堕落之灵", "position": [300, 200], "respawn_time": 30, "priority": 5},
                        {"name": "圣堕落之灵", "position": [500, 250], "respawn_time": 30, "priority": 4},
                        {"name": "圣堕落之灵", "position": [700, 300], "respawn_time": 30, "priority": 3},
                        {"name": "圣堕落之灵", "position": [400, 400], "respawn_time": 30, "priority": 2},
                        {"name": "圣堕落之灵", "position": [600, 450], "respawn_time": 30, "priority": 1}
                    ]
                }
            },
            "settings": {
                "click_delay": 0.5,
                "move_delay": 1.0,
                "attack_duration": 5,
                "scan_interval": 2
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        self.parse_config(config)
    
    def parse_config(self, config):
        """解析配置"""
        self.settings = config.get("settings", {})
        
        for map_name, map_data in config["maps"].items():
            bosses = []
            for boss_data in map_data["bosses"]:
                boss = Boss(
                    name=boss_data["name"],
                    position=tuple(boss_data["position"]),
                    respawn_time=boss_data["respawn_time"],
                    priority=boss_data.get("priority", 1)
                )
                bosses.append(boss)
            
            game_map = GameMap(
                name=map_name,
                bosses=bosses,
                center_position=tuple(map_data["center_position"])
            )
            self.maps[map_name] = game_map
    
    def find_wechat_window(self):
        """查找微信窗口"""
        try:
            # 获取屏幕截图
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            
            # 这里可以添加更复杂的窗口识别逻辑
            # 暂时返回整个屏幕区域
            return (0, 0, screenshot.width, screenshot.height)
        except Exception as e:
            print(f"查找微信窗口失败: {e}")
            return None
    
    def click_position(self, x: int, y: int):
        """点击指定位置"""
        try:
            pyautogui.click(x, y)
            time.sleep(self.settings.get("click_delay", 0.5))
            print(f"点击位置: ({x}, {y})")
        except Exception as e:
            print(f"点击失败: {e}")
    
    def move_to_boss(self, boss: Boss):
        """移动到Boss位置"""
        try:
            print(f"移动到Boss: {boss.name} at {boss.position}")
            
            # 点击Boss位置
            self.click_position(boss.position[0], boss.position[1])
            
            # 等待移动完成
            time.sleep(self.settings.get("move_delay", 1.0))
            
            return True
        except Exception as e:
            print(f"移动到Boss失败: {e}")
            return False
    
    def attack_boss(self, boss: Boss):
        """攻击Boss"""
        try:
            print(f"开始攻击Boss: {boss.name}")
            
            # 点击Boss进行攻击
            self.click_position(boss.position[0], boss.position[1])
            
            # 持续攻击一段时间
            attack_duration = self.settings.get("attack_duration", 5)
            start_time = time.time()
            
            while time.time() - start_time < attack_duration:
                if not self.running:
                    break
                
                # 每隔一段时间点击一次确保持续攻击
                self.click_position(boss.position[0], boss.position[1])
                time.sleep(1)
            
            # 记录击杀时间
            boss.last_killed = datetime.now()
            print(f"Boss {boss.name} 攻击完成")
            
            return True
        except Exception as e:
            print(f"攻击Boss失败: {e}")
            return False
    
    def is_boss_available(self, boss: Boss) -> bool:
        """检查Boss是否可攻击"""
        if boss.last_killed is None:
            return True
        
        elapsed = (datetime.now() - boss.last_killed).total_seconds()
        return elapsed >= boss.respawn_time
    
    def get_available_bosses(self, map_name: str) -> List[Boss]:
        """获取可攻击的Boss列表"""
        if map_name not in self.maps:
            return []
        
        available_bosses = []
        for boss in self.maps[map_name].bosses:
            if self.is_boss_available(boss):
                available_bosses.append(boss)
        
        # 按优先级排序
        available_bosses.sort(key=lambda b: b.priority, reverse=True)
        return available_bosses
    
    def auto_farm_map(self, map_name: str):
        """自动刷指定地图的Boss"""
        if map_name not in self.maps:
            print(f"地图 {map_name} 不存在")
            return
        
        print(f"开始自动刷地图: {map_name}")
        self.current_map = map_name
        self.running = True
        
        while self.running:
            try:
                # 获取可攻击的Boss
                available_bosses = self.get_available_bosses(map_name)
                
                if available_bosses:
                    for boss in available_bosses:
                        if not self.running:
                            break
                        
                        print(f"准备攻击Boss: {boss.name}")
                        
                        # 移动到Boss位置
                        if self.move_to_boss(boss):
                            # 攻击Boss
                            self.attack_boss(boss)
                        
                        # 短暂休息
                        time.sleep(2)
                else:
                    print("没有可攻击的Boss，等待重生...")
                    time.sleep(self.settings.get("scan_interval", 2))
                
            except KeyboardInterrupt:
                print("用户中断")
                break
            except Exception as e:
                print(f"自动刷Boss过程中出错: {e}")
                time.sleep(5)
        
        self.running = False
        print("自动刷Boss已停止")
    
    def stop(self):
        """停止自动刷Boss"""
        self.running = False
        print("正在停止自动刷Boss...")
    
    def show_status(self):
        """显示当前状态"""
        print("\n=== 楚汉传奇自动刷Boss工具 ===")
        print(f"当前地图: {self.current_map or '无'}")
        print(f"运行状态: {'运行中' if self.running else '待机'}")
        
        if self.current_map and self.current_map in self.maps:
            print(f"\n地图 {self.current_map} 的Boss状态:")
            for boss in self.maps[self.current_map].bosses:
                status = "可攻击" if self.is_boss_available(boss) else "重生中"
                print(f"  {boss.name} - {status} (优先级: {boss.priority})")
        
        print("\n可用地图:")
        for map_name in self.maps.keys():
            print(f"  - {map_name}")

def main():
    """主函数"""
    bot = ChuhanLegendBot()
    
    print("楚汉传奇自动刷Boss工具")
    print("=" * 30)
    print("命令:")
    print("  start <地图名> - 开始自动刷指定地图")
    print("  stop - 停止自动刷Boss")
    print("  status - 显示状态")
    print("  quit - 退出程序")
    print("=" * 30)
    
    bot.show_status()
    
    while True:
        try:
            command = input("\n请输入命令: ").strip().split()
            
            if not command:
                continue
            
            if command[0] == "start":
                if len(command) > 1:
                    map_name = " ".join(command[1:])
                    bot.auto_farm_map(map_name)
                else:
                    print("请指定地图名称")
            
            elif command[0] == "stop":
                bot.stop()
            
            elif command[0] == "status":
                bot.show_status()
            
            elif command[0] == "quit":
                bot.stop()
                break
            
            else:
                print("未知命令")
        
        except KeyboardInterrupt:
            print("\n程序被中断")
            bot.stop()
            break
        except Exception as e:
            print(f"执行命令时出错: {e}")

if __name__ == "__main__":
    main()
