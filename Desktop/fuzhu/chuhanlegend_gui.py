#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
楚汉传奇自动刷Boss GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
from chuhanlegend_bot import ChuhanLegendBot

class ChuhanLegendGUI:
    def __init__(self):
        self.bot = ChuhanLegendBot()
        
        self.root = tk.Tk()
        self.root.title("楚汉传奇自动刷Boss工具")
        self.root.geometry("800x600")
        
        self.setup_ui()
        self.update_status()
        
        # 定时更新状态
        self.auto_update()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="楚汉传奇自动刷Boss工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 地图选择
        ttk.Label(control_frame, text="选择地图:").grid(row=0, column=0, sticky=tk.W)
        self.map_var = tk.StringVar()
        self.map_combo = ttk.Combobox(control_frame, textvariable=self.map_var, width=20)
        self.map_combo.grid(row=0, column=1, padx=10, sticky=tk.W)
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=1, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始自动刷Boss", command=self.start_farming)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_farming, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="刷新状态", command=self.update_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="坐标工具", command=self.open_coordinate_picker).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="10")
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        status_frame.rowconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="状态: 待机", font=("Arial", 12))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.current_map_label = ttk.Label(status_frame, text="当前地图: 无")
        self.current_map_label.grid(row=1, column=0, sticky=tk.W, pady=5)
        
        # Boss状态
        boss_frame = ttk.LabelFrame(main_frame, text="Boss状态", padding="10")
        boss_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        boss_frame.columnconfigure(0, weight=1)
        boss_frame.rowconfigure(0, weight=1)
        
        # Boss列表
        self.boss_tree = ttk.Treeview(boss_frame, columns=('status', 'priority', 'respawn'), show='tree headings')
        self.boss_tree.heading('#0', text='Boss名称')
        self.boss_tree.heading('status', text='状态')
        self.boss_tree.heading('priority', text='优先级')
        self.boss_tree.heading('respawn', text='重生时间')
        
        self.boss_tree.column('#0', width=120)
        self.boss_tree.column('status', width=80)
        self.boss_tree.column('priority', width=60)
        self.boss_tree.column('respawn', width=100)
        
        self.boss_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        boss_scrollbar = ttk.Scrollbar(boss_frame, orient="vertical", command=self.boss_tree.yview)
        boss_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.boss_tree.configure(yscrollcommand=boss_scrollbar.set)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 绑定地图选择事件
        self.map_combo.bind('<<ComboboxSelected>>', self.on_map_select)
    
    def update_status(self):
        """更新界面状态"""
        # 更新地图列表
        map_names = list(self.bot.maps.keys())
        self.map_combo['values'] = map_names
        
        if map_names and not self.map_var.get():
            self.map_var.set(map_names[0])
        
        # 更新状态标签
        if self.bot.running:
            self.status_label.config(text="状态: 运行中", foreground="green")
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
        else:
            self.status_label.config(text="状态: 待机", foreground="blue")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
        
        if self.bot.current_map:
            self.current_map_label.config(text=f"当前地图: {self.bot.current_map}")
        else:
            self.current_map_label.config(text="当前地图: 无")
        
        # 更新Boss状态
        self.update_boss_status()
    
    def update_boss_status(self):
        """更新Boss状态显示"""
        # 清空现有项目
        for item in self.boss_tree.get_children():
            self.boss_tree.delete(item)
        
        selected_map = self.map_var.get()
        if selected_map and selected_map in self.bot.maps:
            current_time = datetime.now()
            
            for boss in self.bot.maps[selected_map].bosses:
                if boss.last_killed is None:
                    status = "可攻击"
                    respawn_info = f"{boss.respawn_time}秒"
                else:
                    elapsed = (current_time - boss.last_killed).total_seconds()
                    if elapsed >= boss.respawn_time:
                        status = "可攻击"
                        respawn_info = f"{boss.respawn_time}秒"
                    else:
                        remaining = boss.respawn_time - elapsed
                        status = "重生中"
                        respawn_info = f"{int(remaining)}秒"
                
                self.boss_tree.insert('', 'end', text=boss.name, 
                                    values=(status, boss.priority, respawn_info))
    
    def on_map_select(self, event):
        """地图选择事件"""
        self.update_boss_status()
    
    def start_farming(self):
        """开始自动刷Boss"""
        selected_map = self.map_var.get()
        if not selected_map:
            messagebox.showwarning("警告", "请选择一个地图")
            return
        
        if selected_map not in self.bot.maps:
            messagebox.showerror("错误", f"地图 {selected_map} 不存在")
            return
        
        # 在新线程中运行自动刷Boss
        def run_farming():
            self.bot.auto_farm_map(selected_map)
        
        self.farming_thread = threading.Thread(target=run_farming, daemon=True)
        self.farming_thread.start()
        
        self.log(f"开始自动刷地图: {selected_map}")
        self.update_status()
    
    def stop_farming(self):
        """停止自动刷Boss"""
        self.bot.stop()
        self.log("停止自动刷Boss")
        self.update_status()
    
    def open_coordinate_picker(self):
        """打开坐标获取工具"""
        try:
            import subprocess
            subprocess.Popen(["python3", "coordinate_picker.py"])
            self.log("已启动坐标获取工具")
        except Exception as e:
            messagebox.showerror("错误", f"启动坐标工具失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")
    
    def auto_update(self):
        """自动更新界面"""
        self.update_boss_status()
        self.update_status()
        # 每3秒更新一次
        self.root.after(3000, self.auto_update)
    
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """关闭窗口时的处理"""
        if self.bot.running:
            if messagebox.askokcancel("退出", "自动刷Boss正在运行，确定要退出吗？"):
                self.bot.stop()
                self.root.destroy()
        else:
            self.root.destroy()

if __name__ == "__main__":
    app = ChuhanLegendGUI()
    app.run()
