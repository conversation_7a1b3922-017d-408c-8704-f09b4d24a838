import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import json

class CoordinateHelper:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("坐标获取工具")
        self.root.geometry("400x300")
        
        self.coordinates = []
        self.setup_ui()
        
    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 说明
        ttk.Label(main_frame, text="点击按钮后，移动鼠标到目标位置并按空格键获取坐标").grid(row=0, column=0, columnspan=2, pady=10)
        
        # 按钮
        ttk.Button(main_frame, text="获取地图按钮坐标", command=self.get_map_coordinate).grid(row=1, column=0, padx=5, pady=5)
        ttk.But<PERSON>(main_frame, text="获取Boss坐标", command=self.get_boss_coordinate).grid(row=1, column=1, padx=5, pady=5)
        
        # 坐标显示
        self.coord_text = tk.Text(main_frame, height=10, width=50)
        self.coord_text.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 保存按钮
        ttk.Button(main_frame, text="保存到配置文件", command=self.save_coordinates).grid(row=3, column=0, columnspan=2, pady=5)
        
        # 绑定键盘事件
        self.root.bind('<KeyPress-space>', self.capture_coordinate)
        self.root.focus_set()
        
        self.waiting_for_coordinate = False
        self.coordinate_type = ""
        
    def get_map_coordinate(self):
        self.waiting_for_coordinate = True
        self.coordinate_type = "地图按钮"
        self.coord_text.insert(tk.END, f"请移动鼠标到{self.coordinate_type}位置，然后按空格键...\n")
        
    def get_boss_coordinate(self):
        self.waiting_for_coordinate = True
        self.coordinate_type = "Boss"
        self.coord_text.insert(tk.END, f"请移动鼠标到{self.coordinate_type}位置，然后按空格键...\n")
        
    def capture_coordinate(self, event):
        if self.waiting_for_coordinate:
            x, y = pyautogui.position()
            self.coordinates.append({
                "type": self.coordinate_type,
                "position": [x, y]
            })
            self.coord_text.insert(tk.END, f"{self.coordinate_type}坐标: [{x}, {y}]\n")
            self.waiting_for_coordinate = False
            
    def save_coordinates(self):
        if not self.coordinates:
            messagebox.showwarning("警告", "没有坐标数据可保存")
            return
            
        # 保存到文件
        with open('coordinates.json', 'w', encoding='utf-8') as f:
            json.dump(self.coordinates, f, ensure_ascii=False, indent=2)
            
        messagebox.showinfo("成功", f"已保存 {len(self.coordinates)} 个坐标到 coordinates.json")
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    helper = CoordinateHelper()
    helper.run()