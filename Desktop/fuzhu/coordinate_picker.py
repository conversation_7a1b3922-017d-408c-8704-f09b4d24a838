#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标获取工具
用于获取Boss位置的精确坐标
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import json
import os
from PIL import Image, ImageTk

class CoordinatePicker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("楚汉传奇坐标获取工具")
        self.root.geometry("800x600")
        
        self.coordinates = []
        self.boss_data = []
        self.current_map = "萃光高地"
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架 - 使用pack布局
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 说明文字
        info_label = ttk.Label(main_frame, text="点击'获取坐标'按钮，然后点击游戏中的Boss位置")
        info_label.pack(pady=(0, 10))

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(pady=(0, 10))

        ttk.Button(control_frame, text="获取坐标", command=self.get_coordinate).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="截取屏幕", command=self.take_screenshot).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清空列表", command=self.clear_coordinates).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        
        # 地图名称输入
        map_frame = ttk.Frame(main_frame)
        map_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(map_frame, text="地图名称:").pack(side=tk.LEFT)
        self.map_entry = ttk.Entry(map_frame, width=20)
        self.map_entry.pack(side=tk.LEFT, padx=5)
        self.map_entry.insert(0, self.current_map)
        
        # Boss信息输入
        boss_frame = ttk.LabelFrame(main_frame, text="Boss信息", padding="5")
        boss_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(boss_frame, text="Boss名称:").grid(row=0, column=0, sticky=tk.W)
        self.boss_name_entry = ttk.Entry(boss_frame, width=15)
        self.boss_name_entry.grid(row=0, column=1, padx=5)
        self.boss_name_entry.insert(0, "圣堕落之灵")
        
        ttk.Label(boss_frame, text="重生时间(秒):").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.respawn_entry = ttk.Entry(boss_frame, width=10)
        self.respawn_entry.grid(row=0, column=3, padx=5)
        self.respawn_entry.insert(0, "30")
        
        ttk.Label(boss_frame, text="优先级(1-5):").grid(row=0, column=4, sticky=tk.W, padx=(10, 0))
        self.priority_entry = ttk.Entry(boss_frame, width=5)
        self.priority_entry.grid(row=0, column=5, padx=5)
        self.priority_entry.insert(0, "5")
        
        ttk.Button(boss_frame, text="添加Boss", command=self.add_boss).grid(row=0, column=6, padx=10)
        
        # 坐标列表
        list_frame = ttk.LabelFrame(main_frame, text="已获取的坐标", padding="5")
        list_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        self.coord_tree = ttk.Treeview(list_frame, columns=('x', 'y', 'name', 'respawn', 'priority'), show='headings')
        self.coord_tree.heading('x', text='X坐标')
        self.coord_tree.heading('y', text='Y坐标')
        self.coord_tree.heading('name', text='Boss名称')
        self.coord_tree.heading('respawn', text='重生时间')
        self.coord_tree.heading('priority', text='优先级')
        
        self.coord_tree.column('x', width=80)
        self.coord_tree.column('y', width=80)
        self.coord_tree.column('name', width=120)
        self.coord_tree.column('respawn', width=80)
        self.coord_tree.column('priority', width=60)
        
        self.coord_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.coord_tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.coord_tree.configure(yscrollcommand=scrollbar.set)
        
        # 删除按钮
        ttk.Button(list_frame, text="删除选中", command=self.delete_selected).grid(row=1, column=0, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="准备就绪")
        self.status_label.grid(row=5, column=0, columnspan=3, sticky=tk.W)
    
    def get_coordinate(self):
        """获取鼠标点击坐标"""
        self.status_label.config(text="请在3秒后点击目标位置...")
        self.root.update()
        
        # 3秒倒计时
        for i in range(3, 0, -1):
            self.status_label.config(text=f"请在{i}秒后点击目标位置...")
            self.root.update()
            self.root.after(1000)
        
        self.status_label.config(text="请点击目标位置...")
        self.root.update()
        
        # 隐藏窗口
        self.root.withdraw()
        
        try:
            # 等待用户点击
            import time
            time.sleep(0.5)
            
            # 获取当前鼠标位置
            x, y = pyautogui.position()
            
            # 恢复窗口
            self.root.deiconify()
            
            self.coordinates.append((x, y))
            self.status_label.config(text=f"获取坐标: ({x}, {y})")
            
            # 自动添加到Boss列表
            self.add_boss_with_coords(x, y)
            
        except Exception as e:
            self.root.deiconify()
            self.status_label.config(text=f"获取坐标失败: {e}")
    
    def add_boss_with_coords(self, x, y):
        """使用指定坐标添加Boss"""
        name = self.boss_name_entry.get() or "圣堕落之灵"
        respawn = self.respawn_entry.get() or "30"
        priority = self.priority_entry.get() or "5"
        
        try:
            respawn_int = int(respawn)
            priority_int = int(priority)
            
            boss_info = {
                "name": name,
                "position": [x, y],
                "respawn_time": respawn_int,
                "priority": priority_int
            }
            
            self.boss_data.append(boss_info)
            
            # 添加到列表显示
            self.coord_tree.insert('', 'end', values=(x, y, name, respawn, priority))
            
        except ValueError:
            messagebox.showerror("错误", "重生时间和优先级必须是数字")
    
    def add_boss(self):
        """手动添加Boss"""
        if len(self.coordinates) == 0:
            messagebox.showwarning("警告", "请先获取坐标")
            return
        
        # 使用最后一个坐标
        x, y = self.coordinates[-1]
        self.add_boss_with_coords(x, y)
    
    def delete_selected(self):
        """删除选中的项目"""
        selected = self.coord_tree.selection()
        if selected:
            for item in selected:
                index = self.coord_tree.index(item)
                self.coord_tree.delete(item)
                if index < len(self.boss_data):
                    del self.boss_data[index]
    
    def clear_coordinates(self):
        """清空坐标列表"""
        self.coordinates.clear()
        self.boss_data.clear()
        for item in self.coord_tree.get_children():
            self.coord_tree.delete(item)
        self.status_label.config(text="坐标列表已清空")
    
    def take_screenshot(self):
        """截取屏幕"""
        try:
            screenshot = pyautogui.screenshot()
            screenshot.save("screenshot.png")
            self.status_label.config(text="屏幕截图已保存为 screenshot.png")
        except Exception as e:
            self.status_label.config(text=f"截图失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        if not self.boss_data:
            messagebox.showwarning("警告", "没有Boss数据可保存")
            return
        
        map_name = self.map_entry.get() or self.current_map
        
        config = {
            "maps": {
                map_name: {
                    "center_position": [640, 400],  # 默认中心位置
                    "bosses": self.boss_data
                }
            },
            "settings": {
                "click_delay": 0.5,
                "move_delay": 1.0,
                "attack_duration": 5,
                "scan_interval": 2
            }
        }
        
        # 如果配置文件已存在，合并数据
        config_file = "chuhanlegend_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                    existing_config["maps"][map_name] = config["maps"][map_name]
                    config = existing_config
            except Exception as e:
                print(f"读取现有配置失败: {e}")
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", f"配置已保存到 {config_file}")
            self.status_label.config(text="配置保存成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CoordinatePicker()
    app.run()
