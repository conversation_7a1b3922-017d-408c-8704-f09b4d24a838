#!/bin/bash

echo "传奇游戏Boss管理工具 - Mac安装脚本"
echo "=================================="

# 检查是否安装了Python3
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3"
    echo "请先安装Python3: https://www.python.org/downloads/"
    exit 1
fi

echo "✅ Python3 已安装"

# 检查是否安装了pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ 未找到pip3"
    echo "正在安装pip..."
    python3 -m ensurepip --upgrade
fi

echo "✅ pip3 已安装"

# 创建虚拟环境（可选）
read -p "是否创建虚拟环境? (y/n): " create_venv
if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ 虚拟环境已创建并激活"
fi

# 安装依赖
echo "安装依赖包..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ 依赖包安装成功"
else
    echo "❌ 依赖包安装失败"
    echo "尝试手动安装:"
    echo "pip3 install opencv-python pyautogui pillow numpy"
    exit 1
fi

# 设置执行权限
chmod +x run.sh

echo ""
echo "🎉 安装完成!"
echo ""
echo "接下来的步骤:"
echo "1. 运行坐标获取工具: python3 coordinate_helper.py"
echo "2. 配置 legend_config.json 文件"
echo "3. 启动主程序: ./run.sh 或 python3 legend_gui.py"
echo ""
echo "⚠️  重要提醒:"
echo "- 需要在系统偏好设置中授予辅助功能权限"
echo "- 需要授予屏幕录制权限"
echo ""