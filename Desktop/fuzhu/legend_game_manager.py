import cv2
import numpy as np
import pyautogui
import time
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class Boss:
    name: str
    position: tuple
    respawn_time: int  # 重生时间(分钟)
    last_killed: Optional[datetime] = None
    priority: int = 1  # 优先级 1-5

@dataclass
class GameMap:
    name: str
    bosses: List[Boss]
    map_button_pos: tuple  # 地图切换按钮位置

class LegendGameManager:
    def __init__(self):
        self.maps: Dict[str, GameMap] = {}
        self.current_map = None
        self.config = self.load_config()
        self.running = False
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open('legend_config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        config = {
            "maps": {
                "辉光高地": {
                    "map_button": [100, 200],
                    "bosses": [
                        {"name": "圣堕落之灵", "position": [400, 300], "respawn_time": 30, "priority": 5},
                        {"name": "圣堕落之灵", "position": [600, 400], "respawn_time": 30, "priority": 4}
                    ]
                }
            },
            "auto_settings": {
                "battle_duration": 10,
                "map_switch_delay": 3,
                "boss_scan_interval": 5
            }
        }
        with open('legend_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return config
    
    def initialize_maps(self):
        """初始化地图数据"""
        for map_name, map_data in self.config["maps"].items():
            bosses = []
            for boss_data in map_data["bosses"]:
                boss = Boss(
                    name=boss_data["name"],
                    position=tuple(boss_data["position"]),
                    respawn_time=boss_data["respawn_time"],
                    priority=boss_data.get("priority", 1)
                )
                bosses.append(boss)
            
            game_map = GameMap(
                name=map_name,
                bosses=bosses,
                map_button_pos=tuple(map_data["map_button"])
            )
            self.maps[map_name] = game_map
    
    def switch_to_map(self, map_name: str):
        """切换到指定地图"""
        if map_name not in self.maps:
            print(f"地图 {map_name} 不存在")
            return False
        
        game_map = self.maps[map_name]
        print(f"切换到地图: {map_name}")
        
        # 点击地图按钮
        pyautogui.click(game_map.map_button_pos)
        time.sleep(self.config["auto_settings"]["map_switch_delay"])
        
        self.current_map = map_name
        return True
    
    def get_available_bosses(self, map_name: str) -> List[Boss]:
        """获取可攻击的boss列表"""
        if map_name not in self.maps:
            return []
        
        available_bosses = []
        current_time = datetime.now()
        
        for boss in self.maps[map_name].bosses:
            if boss.last_killed is None:
                available_bosses.append(boss)
            else:
                respawn_time = boss.last_killed + timedelta(minutes=boss.respawn_time)
                if current_time >= respawn_time:
                    available_bosses.append(boss)
        
        # 按优先级排序
        available_bosses.sort(key=lambda x: x.priority, reverse=True)
        return available_bosses
    
    def attack_boss(self, boss: Boss):
        """攻击指定boss"""
        print(f"攻击boss: {boss.name} 位置: {boss.position}")
        
        # 点击boss位置
        pyautogui.click(boss.position)
        
        # 等待战斗完成
        battle_duration = self.config["auto_settings"]["battle_duration"]
        time.sleep(battle_duration)
        
        # 记录击杀时间
        boss.last_killed = datetime.now()
        print(f"击杀完成，下次重生时间: {boss.last_killed + timedelta(minutes=boss.respawn_time)}")
    
    def auto_farm_all_maps(self):
        """自动刷所有地图的boss"""
        self.running = True
        print("开始自动刷boss...")
        
        while self.running:
            try:
                for map_name in self.maps.keys():
                    if not self.running:
                        break
                    
                    # 切换地图
                    if not self.switch_to_map(map_name):
                        continue
                    
                    # 获取可攻击的boss
                    available_bosses = self.get_available_bosses(map_name)
                    
                    if available_bosses:
                        print(f"地图 {map_name} 发现 {len(available_bosses)} 个可攻击boss")
                        
                        # 攻击优先级最高的boss
                        self.attack_boss(available_bosses[0])
                    else:
                        print(f"地图 {map_name} 暂无可攻击boss")
                
                # 扫描间隔
                time.sleep(self.config["auto_settings"]["boss_scan_interval"])
                
            except KeyboardInterrupt:
                self.stop()
                break
            except Exception as e:
                print(f"错误: {e}")
                time.sleep(2)
    
    def stop(self):
        """停止自动化"""
        self.running = False
        print("停止自动刷boss")
    
    def show_status(self):
        """显示所有地图boss状态"""
        current_time = datetime.now()
        
        for map_name, game_map in self.maps.items():
            print(f"\n=== {map_name} ===")
            for boss in game_map.bosses:
                if boss.last_killed is None:
                    status = "可攻击"
                else:
                    respawn_time = boss.last_killed + timedelta(minutes=boss.respawn_time)
                    if current_time >= respawn_time:
                        status = "可攻击"
                    else:
                        remaining = respawn_time - current_time
                        status = f"重生倒计时: {remaining.seconds//60}分{remaining.seconds%60}秒"
                
                print(f"  {boss.name} (优先级:{boss.priority}) - {status}")

if __name__ == "__main__":
    manager = LegendGameManager()
    manager.initialize_maps()
    
    # 显示当前状态
    manager.show_status()
    
    # 开始自动刷boss
    manager.auto_farm_all_maps()