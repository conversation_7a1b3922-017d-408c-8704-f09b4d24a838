import tkinter as tk
from tkinter import ttk, messagebox
import threading
from legend_game_manager import <PERSON>GameManager
from datetime import datetime, timedelta

class LegendGameGUI:
    def __init__(self):
        self.manager = LegendGameManager()
        self.manager.initialize_maps()
        
        self.root = tk.Tk()
        self.root.title("传奇游戏Boss管理工具")
        self.root.geometry("900x700")
        
        self.setup_ui()
        self.update_status()
        
        # 定时更新状态
        self.auto_update()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 控制按钮
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="5")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="开始自动刷boss", command=self.start_auto).grid(row=0, column=0, padx=5)
        ttk.Button(control_frame, text="停止", command=self.stop_auto).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="刷新状态", command=self.update_status).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="手动切换地图", command=self.manual_switch_map).grid(row=0, column=3, padx=5)
        
        # 地图选择
        map_frame = ttk.LabelFrame(main_frame, text="地图管理", padding="5")
        map_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        map_frame.rowconfigure(0, weight=1)
        
        self.map_listbox = tk.Listbox(map_frame, height=15)
        self.map_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.map_listbox.bind('<<ListboxSelect>>', self.on_map_select)
        
        # Boss状态
        boss_frame = ttk.LabelFrame(main_frame, text="Boss状态", padding="5")
        boss_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        boss_frame.rowconfigure(0, weight=1)
        boss_frame.columnconfigure(0, weight=1)
        
        self.boss_tree = ttk.Treeview(boss_frame, columns=('status', 'priority', 'respawn'), show='tree headings')
        self.boss_tree.heading('#0', text='Boss名称')
        self.boss_tree.heading('status', text='状态')
        self.boss_tree.heading('priority', text='优先级')
        self.boss_tree.heading('respawn', text='重生时间')
        
        self.boss_tree.column('#0', width=150)
        self.boss_tree.column('status', width=100)
        self.boss_tree.column('priority', width=60)
        self.boss_tree.column('respawn', width=80)
        
        self.boss_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        boss_scrollbar = ttk.Scrollbar(boss_frame, orient="vertical", command=self.boss_tree.yview)
        boss_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.boss_tree.configure(yscrollcommand=boss_scrollbar.set)
        
        # 状态信息
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="5")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="状态: 待机")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.current_map_label = ttk.Label(status_frame, text="当前地图: 无")
        self.current_map_label.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 日志
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
    
    def update_status(self):
        """更新界面状态"""
        # 更新地图列表
        self.map_listbox.delete(0, tk.END)
        for map_name in self.manager.maps.keys():
            self.map_listbox.insert(tk.END, map_name)
        
        # 更新boss状态
        self.update_boss_status()
        
        # 更新状态标签
        if self.manager.running:
            self.status_label.config(text="状态: 运行中")
        else:
            self.status_label.config(text="状态: 待机")
        
        if self.manager.current_map:
            self.current_map_label.config(text=f"当前地图: {self.manager.current_map}")
        else:
            self.current_map_label.config(text="当前地图: 无")
    
    def update_boss_status(self):
        """更新boss状态显示"""
        for item in self.boss_tree.get_children():
            self.boss_tree.delete(item)
        
        selected_map = self.get_selected_map()
        if selected_map and selected_map in self.manager.maps:
            current_time = datetime.now()
            
            for boss in self.manager.maps[selected_map].bosses:
                if boss.last_killed is None:
                    status = "可攻击"
                    respawn_info = f"{boss.respawn_time}分钟"
                else:
                    respawn_time = boss.last_killed + timedelta(minutes=boss.respawn_time)
                    if current_time >= respawn_time:
                        status = "可攻击"
                        respawn_info = f"{boss.respawn_time}分钟"
                    else:
                        remaining = respawn_time - current_time
                        status = "重生中"
                        respawn_info = f"{remaining.seconds//60}:{remaining.seconds%60:02d}"
                
                self.boss_tree.insert('', 'end', text=boss.name, 
                                    values=(status, boss.priority, respawn_info))
    
    def get_selected_map(self):
        """获取当前选中的地图"""
        selection = self.map_listbox.curselection()
        if selection:
            return self.map_listbox.get(selection[0])
        return None
    
    def on_map_select(self, event):
        """地图选择事件"""
        self.update_boss_status()
    
    def manual_switch_map(self):
        """手动切换地图"""
        selected_map = self.get_selected_map()
        if selected_map:
            if self.manager.switch_to_map(selected_map):
                self.log(f"手动切换到地图: {selected_map}")
                self.update_status()
            else:
                self.log(f"切换地图失败: {selected_map}")
        else:
            messagebox.showwarning("警告", "请先选择一个地图")
    
    def start_auto(self):
        """开始自动刷boss"""
        if not self.manager.running:
            def run_auto():
                self.manager.auto_farm_all_maps()
            
            thread = threading.Thread(target=run_auto, daemon=True)
            thread.start()
            self.log("开始自动刷boss...")
            self.update_status()
        else:
            messagebox.showinfo("提示", "自动刷boss已在运行中")
    
    def stop_auto(self):
        """停止自动刷boss"""
        self.manager.stop()
        self.log("停止自动刷boss")
        self.update_status()
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def auto_update(self):
        """自动更新界面"""
        self.update_boss_status()
        self.update_status()
        # 每5秒更新一次
        self.root.after(5000, self.auto_update)
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LegendGameGUI()
    app.run()
