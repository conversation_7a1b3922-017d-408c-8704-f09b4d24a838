#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的楚汉传奇自动刷Boss GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
from datetime import datetime
from chuhanlegend_bot import ChuhanLegendBot

class SimpleChuhanLegendGUI:
    def __init__(self):
        self.bot = ChuhanLegendBot()
        
        self.root = tk.Tk()
        self.root.title("楚汉传奇自动刷Boss工具")
        self.root.geometry("600x500")
        
        self.setup_ui()
        self.update_status()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="楚汉传奇自动刷Boss工具", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="15")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 地图选择
        map_frame = ttk.Frame(control_frame)
        map_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(map_frame, text="选择地图:").pack(side=tk.LEFT)
        self.map_var = tk.StringVar()
        self.map_combo = ttk.Combobox(map_frame, textvariable=self.map_var, width=20, state="readonly")
        self.map_combo.pack(side=tk.LEFT, padx=10)
        self.map_combo.bind('<<ComboboxSelected>>', self.on_map_select)
        
        # 按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始自动刷Boss", 
                                      command=self.start_farming)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_farming, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="刷新状态", 
                  command=self.update_status).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="坐标工具", 
                  command=self.open_coordinate_picker).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="15")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_label = ttk.Label(status_frame, text="状态: 待机", 
                                     font=("Arial", 12, "bold"))
        self.status_label.pack(anchor=tk.W)
        
        self.current_map_label = ttk.Label(status_frame, text="当前地图: 无")
        self.current_map_label.pack(anchor=tk.W, pady=2)
        
        # Boss状态
        boss_frame = ttk.LabelFrame(main_frame, text="Boss状态", padding="15")
        boss_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # Boss列表
        self.boss_listbox = tk.Listbox(boss_frame, height=8)
        self.boss_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 日志
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="15")
        log_frame.pack(fill=tk.X)
        
        self.log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
        self.log_text.pack(fill=tk.X)
        
        # 添加初始日志
        self.log("楚汉传奇自动刷Boss工具已启动")
        
    def update_status(self):
        """更新状态"""
        # 更新地图列表
        map_names = list(self.bot.maps.keys())
        self.map_combo['values'] = map_names
        
        if map_names and not self.map_var.get():
            self.map_var.set(map_names[0])
        
        # 更新状态标签
        if self.bot.running:
            self.status_label.config(text="状态: 运行中", foreground="green")
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
        else:
            self.status_label.config(text="状态: 待机", foreground="blue")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
        
        if self.bot.current_map:
            self.current_map_label.config(text=f"当前地图: {self.bot.current_map}")
        else:
            self.current_map_label.config(text="当前地图: 无")
        
        # 更新Boss状态
        self.update_boss_status()
    
    def update_boss_status(self):
        """更新Boss状态"""
        self.boss_listbox.delete(0, tk.END)
        
        selected_map = self.map_var.get()
        if selected_map and selected_map in self.bot.maps:
            for boss in self.bot.maps[selected_map].bosses:
                if boss.last_killed is None:
                    status = "可攻击"
                else:
                    elapsed = (datetime.now() - boss.last_killed).total_seconds()
                    if elapsed >= boss.respawn_time:
                        status = "可攻击"
                    else:
                        remaining = int(boss.respawn_time - elapsed)
                        status = f"重生中({remaining}秒)"
                
                display_text = f"{boss.name} - {status} - 优先级:{boss.priority}"
                self.boss_listbox.insert(tk.END, display_text)
    
    def on_map_select(self, event):
        """地图选择事件"""
        self.update_boss_status()
    
    def start_farming(self):
        """开始自动刷Boss"""
        selected_map = self.map_var.get()
        if not selected_map:
            messagebox.showwarning("警告", "请选择一个地图")
            return
        
        if selected_map not in self.bot.maps:
            messagebox.showerror("错误", f"地图 {selected_map} 不存在")
            return
        
        # 检查是否有Boss配置
        if not self.bot.maps[selected_map].bosses:
            messagebox.showwarning("警告", "该地图没有配置Boss位置，请先使用坐标工具添加Boss")
            return
        
        def run_farming():
            self.bot.auto_farm_map(selected_map)
        
        self.farming_thread = threading.Thread(target=run_farming, daemon=True)
        self.farming_thread.start()
        
        self.log(f"开始自动刷地图: {selected_map}")
        self.update_status()
    
    def stop_farming(self):
        """停止自动刷Boss"""
        self.bot.stop()
        self.log("停止自动刷Boss")
        self.update_status()
    
    def open_coordinate_picker(self):
        """打开坐标获取工具"""
        try:
            import subprocess
            subprocess.Popen(["python3", "simple_coordinate_picker.py"])
            self.log("已启动坐标获取工具")
        except Exception as e:
            messagebox.showerror("错误", f"启动坐标工具失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")
    
    def auto_update(self):
        """自动更新界面"""
        self.update_boss_status()
        self.update_status()
        # 每3秒更新一次
        self.root.after(3000, self.auto_update)
    
    def run(self):
        """运行GUI"""
        # 启动自动更新
        self.auto_update()
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """关闭窗口时的处理"""
        if self.bot.running:
            if messagebox.askokcancel("退出", "自动刷Boss正在运行，确定要退出吗？"):
                self.bot.stop()
                self.root.destroy()
        else:
            self.root.destroy()

if __name__ == "__main__":
    app = SimpleChuhanLegendGUI()
    app.run()
