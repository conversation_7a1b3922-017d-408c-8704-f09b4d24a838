#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的坐标获取工具
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import json
import time

class SimpleCoordinatePicker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("楚汉传奇坐标获取工具")
        self.root.geometry("500x600")
        
        self.coordinates = []
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="坐标获取工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_label = ttk.Label(main_frame, 
                              text="1. 点击'获取坐标'按钮\n2. 3秒后点击游戏中的Boss位置\n3. 填写Boss信息并添加",
                              justify=tk.CENTER)
        info_label.pack(pady=(0, 20))
        
        # 获取坐标按钮
        coord_button = ttk.Button(main_frame, text="获取坐标", 
                                 command=self.get_coordinate)
        coord_button.pack(pady=10)
        
        # Boss信息输入
        info_frame = ttk.LabelFrame(main_frame, text="Boss信息", padding="10")
        info_frame.pack(fill=tk.X, pady=10)
        
        # Boss名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, pady=2)
        ttk.Label(name_frame, text="Boss名称:").pack(side=tk.LEFT)
        self.boss_name_var = tk.StringVar(value="圣堕落之灵")
        ttk.Entry(name_frame, textvariable=self.boss_name_var, width=15).pack(side=tk.LEFT, padx=5)
        
        # 重生时间
        respawn_frame = ttk.Frame(info_frame)
        respawn_frame.pack(fill=tk.X, pady=2)
        ttk.Label(respawn_frame, text="重生时间(秒):").pack(side=tk.LEFT)
        self.respawn_var = tk.StringVar(value="30")
        ttk.Entry(respawn_frame, textvariable=self.respawn_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 优先级
        priority_frame = ttk.Frame(info_frame)
        priority_frame.pack(fill=tk.X, pady=2)
        ttk.Label(priority_frame, text="优先级(1-5):").pack(side=tk.LEFT)
        self.priority_var = tk.StringVar(value="5")
        ttk.Entry(priority_frame, textvariable=self.priority_var, width=5).pack(side=tk.LEFT, padx=5)
        
        # 添加Boss按钮
        ttk.Button(info_frame, text="添加Boss", command=self.add_boss).pack(pady=10)
        
        # 坐标列表
        list_frame = ttk.LabelFrame(main_frame, text="已获取的坐标", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 列表框
        self.coord_listbox = tk.Listbox(list_frame, height=8)
        self.coord_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="删除选中", command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空列表", command=self.clear_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪")
        self.status_label.pack(pady=10)
        
    def get_coordinate(self):
        """获取坐标"""
        try:
            # 倒计时
            for i in range(3, 0, -1):
                self.status_label.config(text=f"请在{i}秒后点击目标位置...")
                self.root.update()
                time.sleep(1)
            
            self.status_label.config(text="请点击目标位置...")
            self.root.update()
            
            # 隐藏窗口
            self.root.withdraw()
            time.sleep(0.5)
            
            # 获取鼠标位置
            x, y = pyautogui.position()
            
            # 恢复窗口
            self.root.deiconify()
            
            self.current_coord = (x, y)
            self.status_label.config(text=f"获取坐标: ({x}, {y})")
            
        except Exception as e:
            self.root.deiconify()
            self.status_label.config(text=f"获取坐标失败: {e}")
    
    def add_boss(self):
        """添加Boss"""
        if not hasattr(self, 'current_coord'):
            messagebox.showwarning("警告", "请先获取坐标")
            return
        
        try:
            name = self.boss_name_var.get() or "圣堕落之灵"
            respawn = int(self.respawn_var.get() or "30")
            priority = int(self.priority_var.get() or "5")
            
            x, y = self.current_coord
            
            boss_info = {
                "name": name,
                "position": [x, y],
                "respawn_time": respawn,
                "priority": priority
            }
            
            self.coordinates.append(boss_info)
            
            # 添加到列表显示
            display_text = f"{name} - ({x}, {y}) - 重生:{respawn}s - 优先级:{priority}"
            self.coord_listbox.insert(tk.END, display_text)
            
            self.status_label.config(text=f"已添加Boss: {name}")
            
        except ValueError:
            messagebox.showerror("错误", "重生时间和优先级必须是数字")
    
    def delete_selected(self):
        """删除选中项"""
        selection = self.coord_listbox.curselection()
        if selection:
            index = selection[0]
            self.coord_listbox.delete(index)
            if index < len(self.coordinates):
                del self.coordinates[index]
    
    def clear_list(self):
        """清空列表"""
        self.coord_listbox.delete(0, tk.END)
        self.coordinates.clear()
        self.status_label.config(text="列表已清空")
    
    def save_config(self):
        """保存配置"""
        if not self.coordinates:
            messagebox.showwarning("警告", "没有Boss数据可保存")
            return
        
        config = {
            "maps": {
                "萃光高地": {
                    "center_position": [640, 400],
                    "bosses": self.coordinates
                }
            },
            "settings": {
                "click_delay": 0.5,
                "move_delay": 1.0,
                "attack_duration": 5,
                "scan_interval": 2
            }
        }
        
        try:
            with open("chuhanlegend_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "配置已保存到 chuhanlegend_config.json")
            self.status_label.config(text="配置保存成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleCoordinatePicker()
    app.run()
