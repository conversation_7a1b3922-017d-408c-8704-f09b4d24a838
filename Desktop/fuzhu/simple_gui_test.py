#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GUI测试程序
用于验证Tkinter是否正常工作
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui

class SimpleGUITest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("楚汉传奇工具测试")
        self.root.geometry("600x400")
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="楚汉传奇自动刷Boss工具", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明文字
        info_text = """
        这是一个简化的测试界面，用于验证GUI是否正常显示。
        
        如果你能看到这些文字和下面的按钮，说明界面正常工作。
        """
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.CENTER)
        info_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 测试按钮
        ttk.Button(button_frame, text="测试鼠标位置", 
                  command=self.test_mouse_position).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="测试截图", 
                  command=self.test_screenshot).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="显示消息", 
                  command=self.show_message).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_label = ttk.Label(main_frame, text="状态: 准备就绪", 
                                     font=("Arial", 12))
        self.status_label.pack(pady=20)
        
        # 文本框
        text_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        self.log_text = tk.Text(text_frame, height=8, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加初始日志
        self.log("GUI界面初始化完成")
        self.log("如果你能看到这条消息，说明界面正常工作")
    
    def test_mouse_position(self):
        """测试获取鼠标位置"""
        try:
            x, y = pyautogui.position()
            message = f"当前鼠标位置: ({x}, {y})"
            self.status_label.config(text=message)
            self.log(message)
        except Exception as e:
            error_msg = f"获取鼠标位置失败: {e}"
            self.status_label.config(text=error_msg)
            self.log(error_msg)
    
    def test_screenshot(self):
        """测试截图功能"""
        try:
            screenshot = pyautogui.screenshot()
            screenshot.save("test_screenshot.png")
            message = "截图成功，已保存为 test_screenshot.png"
            self.status_label.config(text=message)
            self.log(message)
        except Exception as e:
            error_msg = f"截图失败: {e}"
            self.status_label.config(text=error_msg)
            self.log(error_msg)
    
    def show_message(self):
        """显示消息框"""
        messagebox.showinfo("测试", "GUI界面工作正常！")
        self.log("显示了测试消息框")
    
    def log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    print("启动简单GUI测试...")
    app = SimpleGUITest()
    app.run()
