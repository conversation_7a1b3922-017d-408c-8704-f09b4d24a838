#!/bin/bash

echo "楚汉传奇自动刷Boss工具"
echo "====================="
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖包..."
python3 -c "import cv2, pyautogui, numpy, PIL" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    pip3 install opencv-python pyautogui Pillow numpy
fi

echo "启动楚汉传奇自动刷Boss工具..."
export TK_SILENCE_DEPRECATION=1
python3 chuhanlegend_gui.py
