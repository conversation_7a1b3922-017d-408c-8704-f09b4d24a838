#!/bin/bash

echo "启动楚汉传奇自动刷Boss工具"
echo "=========================="
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python"
    exit 1
fi

# 设置环境变量
export TK_SILENCE_DEPRECATION=1

echo "启动主界面..."
python3 simple_chuhanlegend_gui.py &

echo "启动坐标获取工具..."
python3 simple_coordinate_picker.py &

echo ""
echo "两个工具已启动："
echo "1. 楚汉传奇自动刷Boss主界面"
echo "2. 坐标获取工具"
echo ""
echo "使用说明："
echo "1. 先用坐标工具获取Boss位置"
echo "2. 保存配置后在主界面开始自动刷Boss"
echo ""

wait
